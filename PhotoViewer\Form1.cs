namespace PhotoViewer;
using System.Drawing;
using System.IO;
using System.Windows.Forms;

public partial class Form1 : Form
{
    private string[] imageFiles = Array.Empty<string>();
    private int currentIndex = 0;
    private float zoomFactor = 1.0f;
    private float displayZoomFactor = 1.0f; // Track the actual display zoom
    private Bitmap? originalImage = null;
    private string imagesPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Images");

    private bool isSelecting;
    private bool isMovingSelection;
    private bool isResizingSelection;
    private int resizeHandleIndex = -1; // 0-7 for 8 handles
    private Point selectionStart;
    private Point moveStart;
    private Rectangle selectionRect;
    private bool hasSelection;
    private Button btnCropSelection = null!;
    private Button btnSave = null!;
    private bool hasCroppedImage = false;
    private const int HANDLE_SIZE = 8;

    private enum HandleType { None = -1, NW, N, NE, E, SE, S, SW, W }

    private float dpiScaleFactor = 1.0f;

    private void ReorderButtonControls()
    {
        // Send the first 5 buttons to the back so crop and save appear on top
        btnPrev.SendToBack();
        btnNext.SendToBack();
        btnZoomIn.SendToBack();
        btnZoomOut.SendToBack();
        btnOpen.SendToBack();
    }

    public Form1(string? initialImagePath = null)
    {
        InitializeComponent();
        
        // Calculate DPI scaling factor
        using (var graphics = CreateGraphics())
        {
            dpiScaleFactor = graphics.DpiX / 96.0f;
        }
        
        CreateCropSelectionButton();
        CreateSaveButton();
        ReorderButtonControls();
        AttachEventHandlers();
        EnableDragAndDrop();

        if (!string.IsNullOrEmpty(initialImagePath) && File.Exists(initialImagePath))
        {
            LoadSingleImage(initialImagePath);
        }
        else
        {
            LoadImages();
        }

        ShowImage();
    }

    private Image? LoadEmbeddedIcon(string iconName)
    {
        try
        {
            using var stream = GetType().Assembly.GetManifestResourceStream($"PhotoViewer.Resources.{iconName}");
            if (stream != null)
            {
                var originalImage = Image.FromStream(stream);
                // Scale icon size based on DPI
                int iconSize = (int)(80 * dpiScaleFactor);
                var resizedImage = new Bitmap(originalImage, new Size(iconSize, iconSize));
                originalImage.Dispose();
                return resizedImage;
            }
            return null;
        }
        catch
        {
            return null;
        }
    }

    private void CreateCropSelectionButton()
    {
        btnCropSelection = new Button();
        int buttonSize = (int)(100 * dpiScaleFactor);
        btnCropSelection.Size = new Size(buttonSize, buttonSize);
        // Calculate position based on scaled panel width
        int xPos = (int)(542 * dpiScaleFactor);
        btnCropSelection.Location = new Point(xPos, (int)(10 * dpiScaleFactor));
        btnCropSelection.Enabled = false;
        btnCropSelection.Click += BtnCropSelection_Click;
        btnCropSelection.Image = LoadEmbeddedIcon("icons8-crop-100.png");
        btnCropSelection.ImageAlign = ContentAlignment.MiddleCenter;
        btnCropSelection.Text = "";
        btnCropSelection.FlatStyle = FlatStyle.Flat;
        buttonPanel.Controls.Add(btnCropSelection);
    }

    private void CreateSaveButton()
    {
        btnSave = new Button();
        int buttonSize = (int)(100 * dpiScaleFactor);
        btnSave.Size = new Size(buttonSize, buttonSize);
        // Calculate position based on scaled panel width
        int xPos = (int)(648 * dpiScaleFactor);
        btnSave.Location = new Point(xPos, (int)(10 * dpiScaleFactor));
        btnSave.Enabled = false;
        btnSave.Click += BtnSave_Click;
        btnSave.Image = LoadEmbeddedIcon("icons8-save-as-100.png");
        btnSave.ImageAlign = ContentAlignment.MiddleCenter;
        btnSave.Text = "";
        btnSave.FlatStyle = FlatStyle.Flat;
        buttonPanel.Controls.Add(btnSave);
    }

    private void EnableDragAndDrop()
    {
        this.AllowDrop = true;
        this.DragEnter += Form1_DragEnter;
        this.DragDrop += Form1_DragDrop;
        pictureBox.AllowDrop = true;
        pictureBox.DragEnter += Form1_DragEnter;
        pictureBox.DragDrop += Form1_DragDrop;
    }

    private void AttachEventHandlers()
    {
        btnPrev.Click += BtnPrev_Click;
        btnNext.Click += BtnNext_Click;
        btnZoomIn.Click += BtnZoomIn_Click;
        btnZoomOut.Click += BtnZoomOut_Click;
        btnOpen.Click += BtnOpen_Click;
        
        // Scale Designer-created buttons based on DPI
        int buttonSize = (int)(100 * dpiScaleFactor);
        btnPrev.Size = new Size(buttonSize, buttonSize);
        btnNext.Size = new Size(buttonSize, buttonSize);
        btnZoomIn.Size = new Size(buttonSize, buttonSize);
        btnZoomOut.Size = new Size(buttonSize, buttonSize);
        btnOpen.Size = new Size(buttonSize, buttonSize);
        
        // Scale button locations
        btnPrev.Location = new Point((int)(12 * dpiScaleFactor), (int)(10 * dpiScaleFactor));
        btnNext.Location = new Point((int)(118 * dpiScaleFactor), (int)(10 * dpiScaleFactor));
        btnZoomIn.Location = new Point((int)(224 * dpiScaleFactor), (int)(10 * dpiScaleFactor));
        btnZoomOut.Location = new Point((int)(330 * dpiScaleFactor), (int)(10 * dpiScaleFactor));
        btnOpen.Location = new Point((int)(436 * dpiScaleFactor), (int)(10 * dpiScaleFactor));
        
        // Scale button panel size
        int panelWidth = (int)(800 * dpiScaleFactor);
        int panelHeight = (int)(120 * dpiScaleFactor);
        buttonPanel.Size = new Size(panelWidth, panelHeight);
        
        // Scale form size
        int formWidth = (int)(800 * dpiScaleFactor);
        int formHeight = (int)(520 * dpiScaleFactor);
        this.Size = new Size(formWidth, formHeight);
        
        // Load embedded icons for Designer-created buttons
        btnPrev.Image = LoadEmbeddedIcon("icons8-prev-100.png");
        btnNext.Image = LoadEmbeddedIcon("icons8-next-100.png");
        btnZoomIn.Image = LoadEmbeddedIcon("icons8-zoom-in-100.png");
        btnZoomOut.Image = LoadEmbeddedIcon("icons8-zoom-out-100.png");
        btnOpen.Image = LoadEmbeddedIcon("icons8-opened-folder-100.png");
        
        // Add mouse events for crop selection
        pictureBox.MouseDown += PictureBox_MouseDown;
        pictureBox.MouseMove += PictureBox_MouseMove;
        pictureBox.MouseUp += PictureBox_MouseUp;
        pictureBox.Paint += PictureBox_Paint;
    }

    private Rectangle GetHandleRect(int index)
    {
        // 0: NW, 1: N, 2: NE, 3: E, 4: SE, 5: S, 6: SW, 7: W
        int x = selectionRect.X;
        int y = selectionRect.Y;
        int w = selectionRect.Width;
        int h = selectionRect.Height;
        int hs = HANDLE_SIZE / 2;
        switch (index)
        {
            case 0: return new Rectangle(x - hs, y - hs, HANDLE_SIZE, HANDLE_SIZE); // NW
            case 1: return new Rectangle(x + w / 2 - hs, y - hs, HANDLE_SIZE, HANDLE_SIZE); // N
            case 2: return new Rectangle(x + w - hs, y - hs, HANDLE_SIZE, HANDLE_SIZE); // NE
            case 3: return new Rectangle(x + w - hs, y + h / 2 - hs, HANDLE_SIZE, HANDLE_SIZE); // E
            case 4: return new Rectangle(x + w - hs, y + h - hs, HANDLE_SIZE, HANDLE_SIZE); // SE
            case 5: return new Rectangle(x + w / 2 - hs, y + h - hs, HANDLE_SIZE, HANDLE_SIZE); // S
            case 6: return new Rectangle(x - hs, y + h - hs, HANDLE_SIZE, HANDLE_SIZE); // SW
            case 7: return new Rectangle(x - hs, y + h / 2 - hs, HANDLE_SIZE, HANDLE_SIZE); // W
            default: return Rectangle.Empty;
        }
    }

    private int HitTestHandle(Point p)
    {
        if (!hasSelection) return -1;
        for (int i = 0; i < 8; i++)
        {
            if (GetHandleRect(i).Contains(p))
                return i;
        }
        return -1;
    }

    private void PictureBox_MouseDown(object? sender, MouseEventArgs e)
    {
        if (originalImage == null) return;

        int handle = HitTestHandle(e.Location);
        if (hasSelection && handle != -1)
        {
            // Start resizing
            isResizingSelection = true;
            resizeHandleIndex = handle;
            moveStart = e.Location;
            pictureBox.Cursor = GetCursorForHandle(handle);
        }
        else if (hasSelection && selectionRect.Contains(e.Location))
        {
            // Start moving the selection
            isMovingSelection = true;
            moveStart = e.Location;
            pictureBox.Cursor = Cursors.SizeAll;
        }
        else
        {
            // Start new selection
            isSelecting = true;
            selectionStart = e.Location;
            selectionRect = new Rectangle(selectionStart, new Size(0, 0));
            hasSelection = false;
            btnCropSelection.Enabled = false;
            pictureBox.Cursor = Cursors.Cross;
        }
        pictureBox.Invalidate();
    }

    private void PictureBox_MouseMove(object? sender, MouseEventArgs e)
    {
        if (originalImage == null) return;

        if (isResizingSelection && hasSelection)
        {
            Rectangle r = selectionRect;
            int minSize = 10;
            int dx = e.X - moveStart.X;
            int dy = e.Y - moveStart.Y;
            switch (resizeHandleIndex)
            {
                case 0: // NW
                    r.X += dx; r.Y += dy; r.Width -= dx; r.Height -= dy; break;
                case 1: // N
                    r.Y += dy; r.Height -= dy; break;
                case 2: // NE
                    r.Y += dy; r.Width += dx; r.Height -= dy; break;
                case 3: // E
                    r.Width += dx; break;
                case 4: // SE
                    r.Width += dx; r.Height += dy; break;
                case 5: // S
                    r.Height += dy; break;
                case 6: // SW
                    r.X += dx; r.Width -= dx; r.Height += dy; break;
                case 7: // W
                    r.X += dx; r.Width -= dx; break;
            }
            // Keep within bounds
            if (r.X < 0) { r.Width += r.X; r.X = 0; }
            if (r.Y < 0) { r.Height += r.Y; r.Y = 0; }
            if (r.Width < minSize) r.Width = minSize;
            if (r.Height < minSize) r.Height = minSize;
            if (r.Right > pictureBox.Width) r.Width = pictureBox.Width - r.X;
            if (r.Bottom > pictureBox.Height) r.Height = pictureBox.Height - r.Y;
            selectionRect = r;
            moveStart = e.Location;
            pictureBox.Invalidate();
        }
        else if (isMovingSelection && hasSelection)
        {
            int dx = e.X - moveStart.X;
            int dy = e.Y - moveStart.Y;
            Rectangle newRect = new Rectangle(selectionRect.X + dx, selectionRect.Y + dy, selectionRect.Width, selectionRect.Height);
            if (newRect.X < 0) newRect.X = 0;
            if (newRect.Y < 0) newRect.Y = 0;
            if (newRect.Right > pictureBox.Width) newRect.X = pictureBox.Width - newRect.Width;
            if (newRect.Bottom > pictureBox.Height) newRect.Y = pictureBox.Height - newRect.Height;
            selectionRect = newRect;
            moveStart = e.Location;
            pictureBox.Invalidate();
        }
        else if (isSelecting)
        {
            int x = Math.Min(selectionStart.X, e.X);
            int y = Math.Min(selectionStart.Y, e.Y);
            int width = Math.Abs(e.X - selectionStart.X);
            int height = Math.Abs(e.Y - selectionStart.Y);
            selectionRect = new Rectangle(x, y, width, height);
            pictureBox.Invalidate();
        }
        else if (hasSelection)
        {
            int handle = HitTestHandle(e.Location);
            if (handle != -1)
            {
                pictureBox.Cursor = GetCursorForHandle(handle);
            }
            else if (selectionRect.Contains(e.Location))
            {
                pictureBox.Cursor = Cursors.SizeAll;
            }
            else
            {
                pictureBox.Cursor = Cursors.Default;
            }
        }
        else
        {
            pictureBox.Cursor = Cursors.Default;
        }
    }

    private void PictureBox_MouseUp(object? sender, MouseEventArgs e)
    {
        if (originalImage == null) return;

        if (isResizingSelection)
        {
            isResizingSelection = false;
            resizeHandleIndex = -1;
            pictureBox.Cursor = Cursors.Default;
        }
        else if (isMovingSelection)
        {
            isMovingSelection = false;
            pictureBox.Cursor = Cursors.Default;
        }
        else if (isSelecting)
        {
            isSelecting = false;
            if (selectionRect.Width > 10 && selectionRect.Height > 10)
            {
                hasSelection = true;
                btnCropSelection.Enabled = true;
            }
            else
            {
                hasSelection = false;
                btnCropSelection.Enabled = false;
            }
            pictureBox.Cursor = Cursors.Default;
        }
        pictureBox.Invalidate();
    }

    private Cursor GetCursorForHandle(int handle)
    {
        switch (handle)
        {
            case 0: return Cursors.SizeNWSE;
            case 1: return Cursors.SizeNS;
            case 2: return Cursors.SizeNESW;
            case 3: return Cursors.SizeWE;
            case 4: return Cursors.SizeNWSE;
            case 5: return Cursors.SizeNS;
            case 6: return Cursors.SizeNESW;
            case 7: return Cursors.SizeWE;
            default: return Cursors.Default;
        }
    }

    private void PictureBox_Paint(object? sender, PaintEventArgs e)
    {
        if (hasSelection || isSelecting)
        {
            // Draw selection rectangle
            using (Pen pen = new Pen(Color.Red, 2))
            {
                pen.DashStyle = System.Drawing.Drawing2D.DashStyle.Dash;
                e.Graphics.DrawRectangle(pen, selectionRect);
            }
            // Draw resize handles
            if (hasSelection)
            {
                using (Brush handleBrush = new SolidBrush(Color.White))
                using (Pen handlePen = new Pen(Color.Black))
                {
                    for (int i = 0; i < 8; i++)
                    {
                        Rectangle handle = GetHandleRect(i);
                        e.Graphics.FillRectangle(handleBrush, handle);
                        e.Graphics.DrawRectangle(handlePen, handle);
                    }
                }
            }
            // Draw semi-transparent overlay
            using (Brush brush = new SolidBrush(Color.FromArgb(100, 0, 0, 0)))
            {
                Rectangle fullRect = new Rectangle(0, 0, pictureBox.Width, pictureBox.Height);
                Region fullRegion = new Region(fullRect);
                fullRegion.Exclude(selectionRect);
                e.Graphics.FillRegion(brush, fullRegion);
            }
        }
    }

    private void BtnCropSelection_Click(object? sender, EventArgs e)
    {
        if (!hasSelection || originalImage == null) return;
        
        try
        {
            // Convert screen coordinates to image coordinates
            Rectangle imageSelection = ConvertScreenToImageCoordinates(selectionRect);
            
            // Validate selection is within image bounds
            if (imageSelection.X < 0 || imageSelection.Y < 0 || 
                imageSelection.Right > originalImage.Width || 
                imageSelection.Bottom > originalImage.Height)
            {
                MessageBox.Show("Selection is outside image bounds", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
            
            // Crop the image
            Bitmap cropped = originalImage.Clone(imageSelection, originalImage.PixelFormat);
            pictureBox.Image?.Dispose();
            pictureBox.Image = cropped;
            originalImage.Dispose();
            originalImage = cropped;
            
            // Clear selection
            hasSelection = false;
            btnCropSelection.Enabled = false;
            pictureBox.Invalidate();
            zoomFactor = 1.0f;
            hasCroppedImage = true;
            btnSave.Enabled = true;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error cropping selection: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private Rectangle ConvertScreenToImageCoordinates(Rectangle screenRect)
    {
        if (originalImage == null || pictureBox.Image == null) return screenRect;
        
        // Get the actual displayed image size and position within the PictureBox
        Size imageSize = pictureBox.Image.Size;
        Point imageLocation = new Point(0, 0);
        
        // Calculate the actual image position (centered in the picture box)
        if (imageSize.Width < pictureBox.Width)
        {
            imageLocation.X = (pictureBox.Width - imageSize.Width) / 2;
        }
        if (imageSize.Height < pictureBox.Height)
        {
            imageLocation.Y = (pictureBox.Height - imageSize.Height) / 2;
        }
        
        // Calculate the scale factor between the displayed image and original image
        float scaleFactorX = (float)originalImage.Width / imageSize.Width;
        float scaleFactorY = (float)originalImage.Height / imageSize.Height;
        
        // Convert screen coordinates to image coordinates
        int imageX = (int)((screenRect.X - imageLocation.X) * scaleFactorX);
        int imageY = (int)((screenRect.Y - imageLocation.Y) * scaleFactorY);
        int imageWidth = (int)(screenRect.Width * scaleFactorX);
        int imageHeight = (int)(screenRect.Height * scaleFactorY);
        
        // Ensure coordinates are within bounds
        imageX = Math.Max(0, Math.Min(imageX, originalImage.Width - 1));
        imageY = Math.Max(0, Math.Min(imageY, originalImage.Height - 1));
        imageWidth = Math.Min(imageWidth, originalImage.Width - imageX);
        imageHeight = Math.Min(imageHeight, originalImage.Height - imageY);
        
        return new Rectangle(imageX, imageY, imageWidth, imageHeight);
    }

    private void Form1_DragEnter(object? sender, DragEventArgs e)
    {
        if (e.Data.GetDataPresent(DataFormats.FileDrop))
            e.Effect = DragDropEffects.Copy;
    }

    private void Form1_DragDrop(object? sender, DragEventArgs e)
    {
        string[] files = (string[])e.Data.GetData(DataFormats.FileDrop);
        if (files.Length > 0)
        {
            string path = files[0];
            if (Directory.Exists(path))
            {
                LoadImagesFromFolder(path);
            }
            else if (File.Exists(path) && IsImageFile(path))
            {
                LoadSingleImage(path);
            }
        }
    }

    private bool IsImageFile(string filePath)
    {
        string ext = Path.GetExtension(filePath).ToLower();
        return ext == ".jpg" || ext == ".jpeg" || ext == ".png" || ext == ".bmp" || ext == ".gif" || ext == ".webp";
    }

    private void LoadImagesFromFolder(string folderPath)
    {
        try
        {
            // Validate the folder path
            if (string.IsNullOrEmpty(folderPath) || !Directory.Exists(folderPath))
            {
                MessageBox.Show("Invalid folder path or directory does not exist.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                imageFiles = Array.Empty<string>();
                currentIndex = 0;
                return;
            }

            var jpgFiles = Directory.GetFiles(folderPath, "*.jpg");
            var jpegFiles = Directory.GetFiles(folderPath, "*.jpeg");
            var pngFiles = Directory.GetFiles(folderPath, "*.png");
            var bmpFiles = Directory.GetFiles(folderPath, "*.bmp");
            var gifFiles = Directory.GetFiles(folderPath, "*.gif");
            var webpFiles = Directory.GetFiles(folderPath, "*.webp");
            
            imageFiles = jpgFiles
                .Concat(jpegFiles)
                .Concat(pngFiles)
                .Concat(bmpFiles)
                .Concat(gifFiles)
                .Concat(webpFiles)
                .ToArray();
            
            currentIndex = 0;
            ClearSelection();
            ShowImage();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error loading images from folder: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            imageFiles = Array.Empty<string>();
            currentIndex = 0;
        }
    }

    private void LoadSingleImage(string imagePath)
    {
        string? directory = null;
        try
        {
            // Validate the image path
            if (string.IsNullOrEmpty(imagePath) || !File.Exists(imagePath))
            {
                MessageBox.Show("Invalid image path or file does not exist.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                imageFiles = Array.Empty<string>();
                currentIndex = 0;
                return;
            }

            // Get the directory of the selected image
            directory = Path.GetDirectoryName(imagePath);
            
            // Check if directory is valid
            if (string.IsNullOrEmpty(directory) || !Directory.Exists(directory))
            {
                MessageBox.Show("Cannot access the directory containing the image.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                // Fallback to single image only
                imageFiles = new string[] { imagePath };
                currentIndex = 0;
                ClearSelection();
                ShowImage();
                return;
            }
            
            // Load all images from the same directory
            var jpgFiles = Directory.GetFiles(directory, "*.jpg");
            var jpegFiles = Directory.GetFiles(directory, "*.jpeg");
            var pngFiles = Directory.GetFiles(directory, "*.png");
            var bmpFiles = Directory.GetFiles(directory, "*.bmp");
            var gifFiles = Directory.GetFiles(directory, "*.gif");
            var webpFiles = Directory.GetFiles(directory, "*.webp");
            
            imageFiles = jpgFiles
                .Concat(jpegFiles)
                .Concat(pngFiles)
                .Concat(bmpFiles)
                .Concat(gifFiles)
                .Concat(webpFiles)
                .ToArray();
            
            // Find the index of the selected image
            currentIndex = Array.IndexOf(imageFiles, imagePath);
            if (currentIndex == -1) currentIndex = 0; // Fallback if not found
            ClearSelection();
            ShowImage();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error loading images from folder:\n{ex.Message}\n\nStack Trace:\n{ex.StackTrace}\n\nimagePath: {imagePath}\ndirectory: {directory}",
                "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            // Fallback to single image only
            imageFiles = new string[] { imagePath };
            currentIndex = 0;
            ClearSelection();
            ShowImage();
        }
    }

    private void LoadImages()
    {
        try
        {
            if (Directory.Exists(imagesPath))
            {
                var jpgFiles = Directory.GetFiles(imagesPath, "*.jpg");
                var pngFiles = Directory.GetFiles(imagesPath, "*.png");
                var gifFiles = Directory.GetFiles(imagesPath, "*.gif");
                var webpFiles = Directory.GetFiles(imagesPath, "*.webp");
                
                imageFiles = jpgFiles
                    .Concat(pngFiles)
                    .Concat(gifFiles)
                    .Concat(webpFiles)
                    .ToArray();
            }
            else
            {
                imageFiles = Array.Empty<string>();
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error loading images from default folder: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            imageFiles = Array.Empty<string>();
        }
    }

    private void ClearSelection()
    {
        hasSelection = false;
        isSelecting = false;
        btnCropSelection.Enabled = false;
        hasCroppedImage = false;
        btnSave.Enabled = false;
        pictureBox.Invalidate();
    }

    private void BtnOpen_Click(object? sender, EventArgs e)
    {
        using (OpenFileDialog openFileDialog = new OpenFileDialog())
        {
            openFileDialog.Filter = "Image files (*.jpg;*.jpeg;*.png;*.bmp;*.gif;*.webp)|*.jpg;*.jpeg;*.png;*.bmp;*.gif;*.webp|All files (*.*)|*.*";
            openFileDialog.Title = "Select Image or Folder";
            openFileDialog.Multiselect = false;

            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                string selectedPath = openFileDialog.FileName;
                if (Directory.Exists(selectedPath))
                {
                    LoadImagesFromFolder(selectedPath);
                }
                else
                {
                    LoadSingleImage(selectedPath);
                }
            }
        }
    }

    private void ShowImage()
    {
        try
        {
            if (imageFiles == null || imageFiles.Length == 0) 
            { 
                pictureBox.Image = null; 
                originalImage?.Dispose();
                originalImage = null;
                ClearSelection();
                UpdateImageInfo();
                return; 
            }
            
            if (currentIndex < 0) currentIndex = 0;
            if (currentIndex >= imageFiles.Length) currentIndex = imageFiles.Length - 1;
            
            // Validate the image file path
            string imagePath = imageFiles[currentIndex];
            if (string.IsNullOrEmpty(imagePath) || !File.Exists(imagePath))
            {
                MessageBox.Show($"Image file not found: {imagePath}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                originalImage = null;
                pictureBox.Image = null;
                ClearSelection();
                UpdateImageInfo();
                return;
            }
            
            originalImage?.Dispose();
            originalImage = new Bitmap(imagePath);
            ClearSelection();
            
            // Calculate initial zoom factor for proper sizing
            zoomFactor = 1.0f;

            // Decide how to display the image
            if (originalImage.Width <= pictureBox.Width && originalImage.Height <= pictureBox.Height)
            {
                // Show at original size, centered
                pictureBox.SizeMode = PictureBoxSizeMode.CenterImage;
                pictureBox.Image = originalImage;
                displayZoomFactor = 1.0f;
            }
            else
            {
                // Show scaled down to fit
                pictureBox.SizeMode = PictureBoxSizeMode.Zoom;
                pictureBox.Image = originalImage;
                displayZoomFactor = 1.0f; // Will be calculated based on actual display
            }
            UpdateImageInfo();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error loading image: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            originalImage = null;
            pictureBox.Image = null;
            ClearSelection();
            UpdateImageInfo();
        }
    }

    private void UpdateImageInfo()
    {
        if (imageFiles.Length == 0 || currentIndex < 0 || currentIndex >= imageFiles.Length)
        {
            lblImageInfo.Text = "No image loaded";
            return;
        }

        try
        {
            string imagePath = imageFiles[currentIndex];
            string fileName = Path.GetFileName(imagePath);
            
            if (originalImage != null)
            {
                // Get file size
                string fileSize = "Unknown";
                try
                {
                    FileInfo fileInfo = new FileInfo(imagePath);
                    fileSize = FormatFileSize(fileInfo.Length);
                }
                catch
                {
                    fileSize = "Unknown";
                }
                
                // Get image dimensions and bit depth
                int width = 0, height = 0, bitDepth = 24;
                try
                {
                    width = originalImage.Width;
                    height = originalImage.Height;
                    bitDepth = GetImageBitDepth(originalImage);
                }
                catch
                {
                    // Use fallback values if we can't get image properties
                    width = 0;
                    height = 0;
                    bitDepth = 24;
                }
                
                // Format the status text
                string statusText = $"File: {fileName} | Size: {fileSize}";
                
                if (width > 0 && height > 0)
                {
                    statusText += $" | Resolution: {width}×{height} | Bit Depth: {bitDepth} bit";
                }
                
                // Add image count if multiple images
                if (imageFiles.Length > 1)
                {
                    statusText += $" | Image {currentIndex + 1} of {imageFiles.Length}";
                }

                // Add zoom level
                int zoomPercent = (int)(displayZoomFactor * 100);
                statusText += $" | Zoom: {zoomPercent}%";
                
                lblImageInfo.Text = statusText;
            }
            else
            {
                lblImageInfo.Text = $"File: {fileName} | Error loading image";
            }
        }
        catch (Exception ex)
        {
            // Show a more specific error message
            lblImageInfo.Text = $"Error updating image info: {ex.Message}";
        }
    }

    private string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB" };
        double len = bytes;
        int order = 0;
        
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        
        return $"{len:0.##} {sizes[order]}";
    }

    private int GetImageBitDepth(Bitmap image)
    {
        try
        {
            return System.Drawing.Image.GetPixelFormatSize(image.PixelFormat);
        }
        catch
        {
            return 24; // Default to 24-bit if we can't determine
        }
    }

    private void BtnPrev_Click(object? sender, EventArgs e)
    {
        if (imageFiles.Length == 0) return;
        currentIndex = (currentIndex - 1 + imageFiles.Length) % imageFiles.Length;
        ShowImage();
    }

    private void BtnNext_Click(object? sender, EventArgs e)
    {
        if (imageFiles.Length == 0) return;
        currentIndex = (currentIndex + 1) % imageFiles.Length;
        ShowImage();
    }

    private void BtnZoomIn_Click(object? sender, EventArgs e)
    {
        if (originalImage == null) return;
        zoomFactor *= 1.2f;
        ApplyZoom();
    }

    private void BtnZoomOut_Click(object? sender, EventArgs e)
    {
        if (originalImage == null) return;
        zoomFactor /= 1.2f;
        ApplyZoom();
    }

    private float CalculateInitialZoomFactor()
    {
        if (originalImage == null) return 1.0f;
        
        // Get the available space in the picture box
        int availableWidth = pictureBox.Width;
        int availableHeight = pictureBox.Height;
        
        // Calculate scale factors to fit the image
        float scaleX = (float)availableWidth / originalImage.Width;
        float scaleY = (float)availableHeight / originalImage.Height;
        
        // If image is smaller than the available space, show at original size
        if (scaleX >= 1.0f && scaleY >= 1.0f)
        {
            return 1.0f;
        }
        
        // If image is larger than available space, scale down to fit
        return Math.Min(scaleX, scaleY);
    }

    private void ApplyZoom()
    {
        if (originalImage == null) return;
        
        try
        {
            // Validate zoom factor
            if (zoomFactor <= 0.01f)
            {
                zoomFactor = 0.01f; // Minimum zoom level
            }
            else if (zoomFactor > 10.0f)
            {
                zoomFactor = 10.0f; // Maximum zoom level
            }
            
            // If zoomFactor is 1.0, just show the image as in ShowImage
            if (Math.Abs(zoomFactor - 1.0f) < 0.01f)
            {
                if (originalImage.Width <= pictureBox.Width && originalImage.Height <= pictureBox.Height)
                {
                    pictureBox.SizeMode = PictureBoxSizeMode.CenterImage;
                    pictureBox.Image = originalImage;
                    displayZoomFactor = 1.0f;
                }
                else
                {
                    pictureBox.SizeMode = PictureBoxSizeMode.Zoom;
                    pictureBox.Image = originalImage;
                    displayZoomFactor = 1.0f;
                }
                UpdateImageInfo();
                return;
            }
            
            // Dispose the current image before creating a new one
            if (pictureBox.Image != null && pictureBox.Image != originalImage)
            {
                pictureBox.Image.Dispose();
                pictureBox.Image = null;
            }
            
            // For manual zoom, always create a scaled bitmap
            int newWidth = Math.Max(1, (int)(originalImage.Width * zoomFactor));
            int newHeight = Math.Max(1, (int)(originalImage.Height * zoomFactor));
            
            // Additional validation
            if (newWidth <= 0 || newHeight <= 0)
            {
                MessageBox.Show($"Invalid dimensions: {newWidth}x{newHeight}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
            
            if (newWidth > 10000 || newHeight > 10000)
            {
                MessageBox.Show("Zoom level too high", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            
            // Validate original image
            if (originalImage.Width <= 0 || originalImage.Height <= 0)
            {
                MessageBox.Show($"Original image has invalid dimensions: {originalImage.Width}x{originalImage.Height}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
            
            Bitmap bmp = new Bitmap(originalImage, newWidth, newHeight);
            pictureBox.SizeMode = PictureBoxSizeMode.CenterImage; // Use CenterImage for manual zoom
            pictureBox.Image = bmp;
            displayZoomFactor = zoomFactor;
            UpdateImageInfo();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error applying zoom: {ex.Message}\nZoom factor: {zoomFactor}\nOriginal size: {originalImage?.Width}x{originalImage?.Height}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void BtnSave_Click(object? sender, EventArgs e)
    {
        if (originalImage == null) return;

        using (SaveFileDialog saveFileDialog = new SaveFileDialog())
        {
            saveFileDialog.Filter = "JPEG files (*.jpg)|*.jpg|PNG files (*.png)|*.png|BMP files (*.bmp)|*.bmp|WebP files (*.webp)|*.webp|All files (*.*)|*.*";
            saveFileDialog.Title = "Save Cropped Image";
            saveFileDialog.DefaultExt = "jpg";
            saveFileDialog.AddExtension = true;

            if (saveFileDialog.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    string extension = Path.GetExtension(saveFileDialog.FileName).ToLower();
                    System.Drawing.Imaging.ImageFormat format = System.Drawing.Imaging.ImageFormat.Jpeg;

                    switch (extension)
                    {
                        case ".png":
                            format = System.Drawing.Imaging.ImageFormat.Png;
                            break;
                        case ".bmp":
                            format = System.Drawing.Imaging.ImageFormat.Bmp;
                            break;
                        case ".webp":
                            format = System.Drawing.Imaging.ImageFormat.Png;
                            break;
                        case ".jpg":
                        case ".jpeg":
                        default:
                            format = System.Drawing.Imaging.ImageFormat.Jpeg;
                            break;
                    }

                    originalImage.Save(saveFileDialog.FileName, format);
                    MessageBox.Show("Image saved successfully!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error saving image: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
    }
}
