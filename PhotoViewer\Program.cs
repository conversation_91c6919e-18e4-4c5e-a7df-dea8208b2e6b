namespace PhotoViewer;

static class Program
{
    /// <summary>
    ///  The main entry point for the application.
    /// </summary>
    [STAThread]
    static void Main(string[] args)
    {
        // To customize application configuration such as set high DPI settings or default font,
        // see https://aka.ms/applicationconfiguration.
        ApplicationConfiguration.Initialize();
        
        // Check if an image file path was passed as a command line argument
        string? initialImagePath = null;
        if (args.Length > 0 && !string.IsNullOrWhiteSpace(args[0]))
        {
            string filePath = args[0];
            // Check if the file exists and is an image file
            if (File.Exists(filePath) && IsImageFile(filePath))
            {
                initialImagePath = filePath;
            }
        }
        
        Application.Run(new Form1(initialImagePath));
    }
    
    private static bool IsImageFile(string filePath)
    {
        string extension = Path.GetExtension(filePath).ToLowerInvariant();
        return extension switch
        {
            ".jpg" or ".jpeg" or ".png" or ".bmp" or ".gif" or ".webp" => true,
            _ => false
        };
    }
}